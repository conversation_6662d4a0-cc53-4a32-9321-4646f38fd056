# AI仪器解析需求
## 一、需求描述
### 1.仪器解析菜单
> 菜单增加'AI仪器解析'模块（通过框架配置进行添加） 

![菜单列表.png](docImg/%E8%8F%9C%E5%8D%95%E5%88%97%E8%A1%A8.png)
### 2. AI仪器解析模块
#### 界面要求：
- 分左中右结构，左侧包括解析准备区域、解析过程显示区域，中间为文件预览区域，右侧解析结果区域，显示比例1:2:1
- 左上区域：
  - 显示标题：解析准备
  - 选择文件：支持文件上传，并在右侧预览区域进行预览
  - 选择仪器（下拉）：如7890A气相色谱仪等
  - 选择解析方式（单选）：图像识别、文本提取
  - 执行解析按钮，点击触发后解析过程显示
- 左下区域：
  - 显示标题：解析过程
  - 横向流程图：开始->文本解析/图像识别->AI解析->结束
  - 执行到某一个步骤时，需要标蓝标识完成，默认为灰色图标
  - 点击步骤可以在右下方显示解析过程的内容文本信息
  - 中间：
  - 显示标题：文件预览，在上传文件后显示文件预览
- 右侧：
  - 显示标题：解析结果
  - 显示按钮：保存
  - 列表：显示采集编号、分析项目、参数名称、参数结果、量纲，其中参数结果支持修改

![AI仪器解析模块.png](docImg/AI%E4%BB%AA%E5%99%A8%E8%A7%A3%E6%9E%90%E6%A8%A1%E5%9D%97.png)

### 3.AI仪器解析配置